import { Injectable } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { Installations } from '../../database/entities';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';

export interface ThreadComment {
  id: string;
  metadata?: {
    external_sinks?: {
      slack?: {
        ts: string;
      };
    };
  };
}

/**
 * Request-scoped cache service for comment threads
 * Eliminates O(n²) performance issues by caching thread data within a single request
 */
@Injectable()
export class CommentThreadCacheService {
  private readonly LOG_SPAN = '[CommentThreadCacheService]';
  
  // Request-scoped cache: parentCommentId -> thread comments
  private threadCache = new Map<string, ThreadComment[]>();
  
  // Request-scoped cache: parentCommentId -> Map<slackTs, commentId>
  private tsToCommentIdCache = new Map<string, Map<string, string>>();

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {}

  /**
   * Get comment threads with caching
   * @param installation Slack installation
   * @param parentCommentId Parent comment ID
   * @returns Array of thread comments
   */
  async getCommentThreads(
    installation: Installations,
    parentCommentId: string,
  ): Promise<ThreadComment[]> {
    // Check cache first
    if (this.threadCache.has(parentCommentId)) {
      this.logger.debug(
        `${this.LOG_SPAN} Cache hit for thread ${parentCommentId}`,
      );
      return this.threadCache.get(parentCommentId)!;
    }

    // Cache miss - fetch from API
    this.logger.debug(
      `${this.LOG_SPAN} Cache miss for thread ${parentCommentId}, fetching from API`,
    );
    
    const threads = await this.thenaPlatformApiProvider.getCommentThreads(
      installation,
      parentCommentId,
    );

    // Cache the result
    this.threadCache.set(parentCommentId, threads);
    
    // Also build the TS -> Comment ID mapping for fast lookups
    this.buildTsToCommentIdMapping(parentCommentId, threads);

    this.logger.debug(
      `${this.LOG_SPAN} Cached ${threads.length} thread comments for ${parentCommentId}`,
    );

    return threads;
  }

  /**
   * Get comment ID by Slack timestamp (optimized O(1) lookup)
   * @param installation Slack installation
   * @param parentCommentId Parent comment ID
   * @param slackTs Slack timestamp
   * @returns Comment ID or null if not found
   */
  async getCommentIdBySlackTs(
    installation: Installations,
    parentCommentId: string,
    slackTs: string,
  ): Promise<string | null> {
    // Ensure we have the thread data cached
    await this.getCommentThreads(installation, parentCommentId);
    
    // Use the optimized TS -> Comment ID mapping
    const tsMapping = this.tsToCommentIdCache.get(parentCommentId);
    if (!tsMapping) {
      return null;
    }

    const commentId = tsMapping.get(slackTs);
    if (commentId) {
      this.logger.debug(
        `${this.LOG_SPAN} Found comment ID ${commentId} for Slack TS ${slackTs}`,
      );
      return commentId;
    }

    this.logger.debug(
      `${this.LOG_SPAN} No comment found for Slack TS ${slackTs} in thread ${parentCommentId}`,
    );
    return null;
  }

  /**
   * Build TS -> Comment ID mapping for fast lookups
   * @param parentCommentId Parent comment ID
   * @param threads Thread comments
   */
  private buildTsToCommentIdMapping(
    parentCommentId: string,
    threads: ThreadComment[],
  ): void {
    const tsMapping = new Map<string, string>();
    
    for (const thread of threads) {
      const slackTs = thread.metadata?.external_sinks?.slack?.ts;
      if (slackTs) {
        tsMapping.set(slackTs, thread.id);
      }
    }
    
    this.tsToCommentIdCache.set(parentCommentId, tsMapping);
    
    this.logger.debug(
      `${this.LOG_SPAN} Built TS mapping with ${tsMapping.size} entries for thread ${parentCommentId}`,
    );
  }

  /**
   * Clear all caches (called at end of request)
   */
  clearCache(): void {
    const threadCount = this.threadCache.size;
    const mappingCount = this.tsToCommentIdCache.size;
    
    this.threadCache.clear();
    this.tsToCommentIdCache.clear();
    
    this.logger.debug(
      `${this.LOG_SPAN} Cleared cache: ${threadCount} thread caches, ${mappingCount} TS mappings`,
    );
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): {
    threadCacheSize: number;
    tsMappingCacheSize: number;
    totalCachedComments: number;
  } {
    let totalCachedComments = 0;
    for (const threads of this.threadCache.values()) {
      totalCachedComments += threads.length;
    }

    return {
      threadCacheSize: this.threadCache.size,
      tsMappingCacheSize: this.tsToCommentIdCache.size,
      totalCachedComments,
    };
  }
}
